'use client';

import React from 'react';
import { useDroppable, useDraggable } from '@dnd-kit/core';

import { AttachmentCard } from './AttachmentCard';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { Badge } from './ui/badge';
import { Button } from './ui/button';
import {
  ChevronDown,
  ChevronRight,
  MoveVertical,
  RefreshCw,
  LockIcon,
  Calendar,
  Clock,
  DollarSign,
  Users,
  File,
  Edit3,
  Trash2
} from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { DragDropType } from '@/utils/drag-drop-types';

interface ContractCardProps {
  contract: any;
  isExpanded: boolean;
  onToggle: () => void;
  isAnyDragging: boolean;
  isDragging: string | null;
  dragOverTarget: string | null;
  getDropPreview: (target: any) => { canDrop: boolean; message: string };
  getDocumentIcon: (type: string) => React.ReactNode;
  editingContractType: string | null;
  setEditingContractType: (id: string | null) => void;
  contractTypes: string[];
  handleContractTypeChange: (contractId: string, type: string) => void;
  handleDeleteDocument: (id: string, type: "unmapped" | "attachment", contractId?: string) => void;
  expandedContracts: Record<string, boolean>;
  toggleContract: (id: string) => void;
}

export const ContractCard: React.FC<ContractCardProps> = ({
  contract,
  isExpanded,
  onToggle,
  isAnyDragging,
  isDragging,
  dragOverTarget,
  getDropPreview,
  getDocumentIcon,
  editingContractType,
  setEditingContractType,
  contractTypes,
  handleContractTypeChange,
  handleDeleteDocument,
  expandedContracts,
  toggleContract,
}) => {
  const isLocked = contract.is_existing && !contract.is_replaced;
  const isReplaced = contract.is_replaced;
  const isImmutable = isLocked || isReplaced;

  const {
    isOver,
    setNodeRef: setDroppableRef,
  } = useDroppable({
    id: contract.id,
    data: { id: contract.id, type: DragDropType.Contract },
    disabled: false,
  });

  const {
    attributes,
    listeners,
    setNodeRef: setDraggableRef,
    transform,
    isDragging: isContractDragging,
  } = useDraggable({
    id: contract.id,
    data: { id: contract.id, type: DragDropType.Contract },
    disabled: isImmutable,
  });

  // Combine refs for both draggable and droppable
  const setNodeRef = (node: HTMLElement | null) => {
    setDroppableRef(node);
    setDraggableRef(node);
  };

  const style = transform ? {
    transform: `translate3d(${transform.x}px, ${transform.y}px, 0)`,
  } : undefined;

  const isHighlighted = isOver && isAnyDragging;

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`border-2 rounded-lg overflow-hidden transition-all duration-200 relative ${
        isAnyDragging ? 'hover:border-blue-300 hover:bg-blue-50' : ''
      } ${
        isHighlighted
          ? 'border-blue-500 bg-blue-100 shadow-lg scale-[1.02] transform'
          : 'border-gray-200'
      } ${
        isContractDragging ? 'opacity-50' : ''
      } ${
        isImmutable ? 'cursor-not-allowed' : 'cursor-move'
      }`}
      {...listeners}
      {...attributes}
    >
      {/* Drop indicator overlay */}
      {isHighlighted && (
        <div className="absolute inset-0 bg-blue-500 bg-opacity-20 pointer-events-none z-10 flex items-center justify-center">
          <div className="bg-blue-600 text-white px-6 py-3 rounded-lg font-medium shadow-lg text-center">
            {getDropPreview({ id: contract.id, type: DragDropType.Contract }).message}
          </div>
        </div>
      )}

      {/* Contract Header */}
      <div
        className={`flex items-center p-4 border-b ${
          isLocked ? 'bg-gray-50 border-gray-200 opacity-75' :
          isReplaced ? 'bg-orange-50 border-orange-200 opacity-80' : ''
        } ${
          isAnyDragging ? 'cursor-move' : 'cursor-pointer hover:bg-gray-50'
        }`}
        onClick={() => isAnyDragging ? null : toggleContract(contract.id)}
      >
        <div className="flex items-center w-full">
          <div className="mr-2">
            {expandedContracts[contract.id] ? (
              <ChevronDown className={`h-5 w-5 ${
                isLocked ? 'text-gray-400' :
                isReplaced ? 'text-orange-500' : 'text-gray-500'
              }`} />
            ) : (
              <ChevronRight className={`h-5 w-5 ${
                isLocked ? 'text-gray-400' :
                isReplaced ? 'text-orange-500' : 'text-gray-500'
              }`} />
            )}
          </div>

          {/* Drag handle icon */}
          <div className={`mr-2 ${isImmutable ? "text-gray-300" : "cursor-move text-gray-400"}`}>
            {isReplaced ? (
              <RefreshCw className="h-4 w-4 text-orange-500 opacity-70" />
            ) : isLocked ? (
              <LockIcon className="h-4 w-4 opacity-70" />
            ) : (
              <MoveVertical className="h-4 w-4" />
            )}
          </div>

          <div className="mr-3">{getDocumentIcon(contract.type)}</div>

          <div className="flex-1">
            <div className="flex items-center justify-between">
              <div className={`font-medium ${
                isLocked ? 'text-gray-500 font-normal opacity-85' :
                isReplaced ? 'text-orange-700' : ''
              }`}>
                {contract.name}
                {isReplaced && <span className="ml-2 text-xs text-orange-500">(Updated)</span>}
                {isLocked && <span className="ml-2 text-xs text-gray-400">(Existing)</span>}
              </div>

              {/* Contract type badge/selector */}
              {editingContractType === contract.id ? (
                <Select
                  open={true}
                  value={contract.contractType}
                  onValueChange={(value) => handleContractTypeChange(contract.id, value)}
                  onOpenChange={(open) => {
                    if (!open) {
                      setEditingContractType(null);
                    }
                  }}
                >
                  <SelectTrigger className="w-32 h-6 text-xs bg-purple-100 border-purple-200">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {contractTypes.filter(type => type !== "All").map((type) => (
                      <SelectItem key={type} value={type} className="text-xs">
                        {type}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              ) : (
                <Badge
                  className="bg-purple-100 text-purple-800 border-purple-200 cursor-pointer hover:bg-purple-200 transition-colors relative group"
                  onClick={(e) => {
                    e.stopPropagation();
                    setEditingContractType(contract.id);
                  }}
                  title="Click to edit contract type"
                >
                  {contract.contractType}
                  <Edit3 className="ml-1 h-3 w-3 opacity-60" />
                </Badge>
              )}
            </div>

            <div className={`text-sm ${
              isLocked ? 'text-gray-400' :
              isReplaced ? 'text-orange-600' : 'text-gray-500'
            }`}>
              {contract.attachments?.length || 0} related document{contract.attachments?.length !== 1 ? "s" : ""}
            </div>
          </div>
        </div>
      </div>

      {/* Expanded Content */}
      {expandedContracts[contract.id] && (
        <div className="p-4 bg-gray-50">
          <Card className={`mb-4 border-l-4 ${isReplaced ? 'border-l-orange-500 bg-orange-50' : 'border-l-blue-500'}`}>
            <CardHeader className="pb-2">
              <CardTitle className={`text-lg ${isReplaced ? 'text-orange-700' : ''}`}>
                {contract.name}
                {isReplaced && <span className="ml-2 text-sm text-orange-500">(Updated)</span>}
              </CardTitle>
            </CardHeader>
            <CardContent className="pb-4">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                <div className="flex items-center">
                  <Calendar className="h-4 w-4 text-gray-500 mr-2" />
                  <div>
                    <p className="text-xs text-gray-500">Effective Date</p>
                    <p className="text-sm font-medium">{contract.startDate}</p>
                  </div>
                </div>
                <div className="flex items-center">
                  <Clock className="h-4 w-4 text-gray-500 mr-2" />
                  <div>
                    <p className="text-xs text-gray-500">Expiry Date</p>
                    <p className="text-sm font-medium">{contract.endDate}</p>
                  </div>
                </div>
                <div className="flex items-center">
                  <DollarSign className="h-4 w-4 text-gray-500 mr-2" />
                  <div>
                    <p className="text-xs text-gray-500">Contract Value</p>
                    <p className="text-sm font-medium">{ }</p>
                  </div>
                </div>
                <div className="flex items-center">
                  <Users className="h-4 w-4 text-gray-500 mr-2" />
                  <div>
                    <p className="text-xs text-gray-500">Parties</p>
                    <p className="text-sm font-medium break-words whitespace-normal">
                      {contract.parties.join(", ")}
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Attachments Section */}
          <div className="ml-6 border-l pl-4 space-y-2">
            <h4 className="text-sm font-medium text-gray-700 mb-3 flex items-center">
              <File className="h-4 w-4 mr-2 text-blue-500" />
              Attachments
              <span className="ml-2 text-xs text-gray-500">(Drag and drop to rearrange)</span>
            </h4>

            {contract.attachments.map((attachment) => (
              <AttachmentCard
                key={attachment.id}
                attachment={attachment}
                contractId={contract.id}
                isDragging={isDragging}
                dragOverTarget={dragOverTarget}
                isAnyDragging={isAnyDragging}
                getDropPreview={getDropPreview}
                handleDeleteDocument={handleDeleteDocument}
              />
            ))}

            {contract.attachments.length === 0 && (
              <div className="text-sm text-gray-500 italic py-2">
                No attachments found. Drag documents here to add.
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};
