'use client';

import React from 'react';
import { useDroppable, useDraggable } from '@dnd-kit/core';

import { Badge } from './ui/badge';
import { Button } from './ui/button';
import {
  MoveVertical,
  RefreshCw,
  LockIcon,
  File,
  Trash2
} from 'lucide-react';
import { DragDropType } from '@/utils/drag-drop-types';

interface AttachmentCardProps {
  attachment: any;
  contractId: string;
  isDragging: string | null;
  dragOverTarget: string | null;
  isAnyDragging: boolean;
  getDropPreview?: (target: any) => { canDrop: boolean; message: string };
  handleDeleteDocument: (id: string, type: string, contractId?: string) => void;
}

export const AttachmentCard: React.FC<AttachmentCardProps> = ({
  attachment,
  contractId,
  isDragging,
  dragOverTarget,
  isAnyDragging,
  getDropPreview,
  handleDeleteDocument,
}) => {
  const isLocked = attachment.is_existing && !attachment.is_replaced;
  const isReplaced = attachment.is_replaced;
  const isImmutable = isLocked || isReplaced;

  const {
    isOver,
    setNodeRef: setDroppableRef,
  } = useDroppable({
    id: attachment.id,
    data: { id: attachment.id, type: DragDropType.Attachment, contractId },
    disabled: false,
  });

  const {
    attributes,
    listeners,
    setNodeRef: setDraggableRef,
    transform,
    isDragging: isAttachmentDragging,
  } = useDraggable({
    id: attachment.id,
    data: { id: attachment.id, type: DragDropType.Attachment, contractId },
    disabled: isImmutable,
  });

  // Combine refs for both draggable and droppable
  const setNodeRef = (node: HTMLElement | null) => {
    setDroppableRef(node);
    setDraggableRef(node);
  };

  const style = transform ? {
    transform: `translate3d(${transform.x}px, ${transform.y}px, 0)`,
  } : undefined;

  const isHighlighted = isOver && isAnyDragging;

  const getConfidenceBadge = (confidence: number) => {
    if (confidence >= 80) return 'bg-green-100 text-green-800 border-green-200';
    if (confidence >= 60) return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    return 'bg-red-100 text-red-800 border-red-200';
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`rounded-md transition-all duration-200 relative ${
        isHighlighted
          ? 'bg-blue-100 border-2 border-blue-500 shadow-lg scale-[1.02] transform'
          : isLocked
            ? "bg-gray-50 border border-gray-200 opacity-75"
            : isReplaced
              ? "bg-orange-50 border border-orange-200 opacity-80"
              : "hover:bg-gray-100 border border-transparent"
      } ${
        isAttachmentDragging ? 'opacity-50' : ''
      } ${
        isImmutable ? 'cursor-not-allowed' : 'cursor-move'
      }`}
      {...listeners}
      {...attributes}
    >
      {/* Drop indicator overlay for attachments */}
      {isHighlighted && (
        <div className="absolute inset-0 bg-blue-500 bg-opacity-20 pointer-events-none z-10 flex items-center justify-center">
          <div className="bg-blue-600 text-white px-4 py-2 rounded-lg font-medium shadow-lg text-center text-sm">
            {getDropPreview ?
              getDropPreview({ id: attachment.id, type: DragDropType.Attachment }).message :
              "Drop here to move attachment"
            }
          </div>
        </div>
      )}
      <div className="flex items-center py-2 px-3">
        <div className={`mr-2 ${isImmutable ? "text-gray-300" : "cursor-move text-gray-400"}`}>
          {isReplaced ? (
            <RefreshCw className="h-4 w-4 text-orange-500 opacity-70" />
          ) : isLocked ? (
            <LockIcon className="h-4 w-4 opacity-70" />
          ) : (
            <MoveVertical className="h-4 w-4" />
          )}
        </div>

        <div className="mr-3">
          <File className={`h-4 w-4 text-gray-500 ${isImmutable ? "opacity-70" : ""}`} />
        </div>

        <div className="flex-1">
          <div className={`text-sm ${isImmutable ? "text-gray-500 font-normal opacity-85" : "font-medium"}`}>
            {attachment.name}
          </div>
          <div className="text-xs text-gray-500">
            {attachment.type}
            {isReplaced && <span className="ml-2 text-xs text-orange-400">(Updated)</span>}
          </div>
        </div>

        <Badge className={`mr-2 ${
          isDragging === attachment.id
            ? 'bg-gray-100 text-gray-800'
            : isReplaced
              ? 'bg-orange-100 text-orange-700 border-orange-200'
              : isLocked
                ? 'bg-gray-100 text-gray-600 border-gray-200'
                : attachment.moved
                  ? 'bg-green-100 text-green-800 border-green-200'
                  : getConfidenceBadge(attachment.confidence)
        }`}>
          {isDragging === attachment.id
            ? 'Dragging'
            : isReplaced
              ? 'Updated'
              : isLocked
                ? 'Existing'
                : attachment.moved
                  ? 'Manual'
                  : `${attachment.confidence}%`}
        </Badge>

        <Button
          variant="ghost"
          size="sm"
          className={`h-8 w-8 p-0 ${isImmutable ? "text-gray-300 cursor-not-allowed" : "text-gray-400 hover:text-red-500"}`}
          onClick={() => !isImmutable && handleDeleteDocument(attachment.id, "attachment", contractId)}
          disabled={isImmutable}
        >
          <Trash2 className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
};
